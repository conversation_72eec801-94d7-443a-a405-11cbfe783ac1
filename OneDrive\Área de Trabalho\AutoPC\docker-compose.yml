
version: '3.9'

services:
  central-agent:
    build: ./agents/central-agent
    ports:
      - "5000:5000"
    environment:
      - TASK_DISTRIBUTION=active

  audio-processor:
    build: ./agents/audio-processor
    depends_on:
      - central-agent

  executor:
    build: ./agents/executor
    depends_on:
      - central-agent

  memory-core:
    build: ./agents/memory-core
    volumes:
      - ./data:/app/data

  voice-assistant:
    build: ./interfaces/voice-assistant
    devices:
      - "/dev/snd:/dev/snd"

  web-ui:
    build: ./interfaces/web-ui
    ports:
      - "8080:80"

  wearable-adapter:
    build: ./interfaces/wearable-adapter"
