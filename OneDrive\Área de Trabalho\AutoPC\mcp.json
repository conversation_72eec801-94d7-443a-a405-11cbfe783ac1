{"project_name": "LLM-PC", "description": "Criação de um cérebro digital local baseado em arquitetura de agentes usando Docker Compose.", "goal": "Simular comportamento inteligente e autônomo em um PC comum, controlado por voz, com mínima latência e modularidade.", "services": ["central-agent", "audio-processor", "executor", "memory-core", "voice-assistant", "web-ui", "wearable-adapter"], "features": {"orchestration": "Tarefas são distribuídas pelo Central Agent.", "voice_interface": "Comandos e respostas por áudio.", "memory_management": "Contexto salvo localmente e reciclado após uso.", "virtualization": "Adaptação da interface para diferentes dispositivos (ex: smartwatch)."}, "deployment": {"platform": "<PERSON><PERSON>", "instructions": "docker-compose up --build"}, "mvp": {"objectives": ["Transcrição e resposta por voz.", "Interface de controle web.", "Execução de comandos simples.", "Histórico e controle de tarefas."]}}