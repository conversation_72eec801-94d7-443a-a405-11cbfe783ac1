
# LLM-PC

Este projeto transforma um computador comum em um "cérebro digital", com controle por voz, orquestração de tarefas, múltiplos agentes especializados e integração com dispositivos como relógios inteligentes.

## Como usar

1. Instale Docker e Docker Compose.
2. Execute: `docker-compose up --build`
3. Acesse a interface web em http://localhost:8080

## Estrutura dos Serviços

- Central Agent: coordena os outros agentes.
- Audio Processor: escuta comandos de voz.
- Executor: executa tarefas do sistema.
- Memory Core: armazena contexto e histórico.
- Voice Assistant: entrada/saída por voz.
- Web UI: interface de controle.
- Wearable Adapter: integração com dispositivos externos.
